import 'package:flutter/material.dart';

class PageTransitions {
  // انتقال منزلق من اليمين (مناسب للعربية)
  static PageRouteBuilder slideFromRight(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  // انتقال منزلق من اليسار
  static PageRouteBuilder slideFromLeft(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(-1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  // انتقال تلاشي مع تكبير
  static PageRouteBuilder fadeScale(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 400),
      reverseTransitionDuration: const Duration(milliseconds: 400),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(
              begin: 0.8,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOutCubic,
            )),
            child: child,
          ),
        );
      },
    );
  }

  // انتقال دوراني مع تلاشي
  static PageRouteBuilder rotationFade(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 500),
      reverseTransitionDuration: const Duration(milliseconds: 500),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: RotationTransition(
            turns: Tween<double>(
              begin: 0.1,
              end: 0.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOutCubic,
            )),
            child: child,
          ),
        );
      },
    );
  }

  // انتقال منزلق من الأسفل
  static PageRouteBuilder slideFromBottom(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 350),
      reverseTransitionDuration: const Duration(milliseconds: 350),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  // انتقال مخصص للحوارات
  static PageRouteBuilder dialogTransition(Widget dialog) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => dialog,
      transitionDuration: const Duration(milliseconds: 300),
      reverseTransitionDuration: const Duration(milliseconds: 300),
      opaque: false,
      barrierColor: Colors.black54,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: Tween<double>(
              begin: 0.7,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOutBack,
            )),
            child: child,
          ),
        );
      },
    );
  }
}

// مساعد للتنقل مع الرسوم المتحركة
class AnimatedNavigator {
  static Future<T?> push<T extends Object?>(
    BuildContext context,
    Widget page, {
    PageTransitionType type = PageTransitionType.slideFromRight,
  }) {
    PageRouteBuilder<T> route;

    switch (type) {
      case PageTransitionType.slideFromRight:
        route = PageTransitions.slideFromRight(page) as PageRouteBuilder<T>;
        break;
      case PageTransitionType.slideFromLeft:
        route = PageTransitions.slideFromLeft(page) as PageRouteBuilder<T>;
        break;
      case PageTransitionType.fadeScale:
        route = PageTransitions.fadeScale(page) as PageRouteBuilder<T>;
        break;
      case PageTransitionType.rotationFade:
        route = PageTransitions.rotationFade(page) as PageRouteBuilder<T>;
        break;
      case PageTransitionType.slideFromBottom:
        route = PageTransitions.slideFromBottom(page) as PageRouteBuilder<T>;
        break;
    }

    return Navigator.of(context).push<T>(route);
  }

  static Future<T?> pushReplacement<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget page, {
    PageTransitionType type = PageTransitionType.slideFromRight,
    TO? result,
  }) {
    PageRouteBuilder<T> route;

    switch (type) {
      case PageTransitionType.slideFromRight:
        route = PageTransitions.slideFromRight(page) as PageRouteBuilder<T>;
        break;
      case PageTransitionType.slideFromLeft:
        route = PageTransitions.slideFromLeft(page) as PageRouteBuilder<T>;
        break;
      case PageTransitionType.fadeScale:
        route = PageTransitions.fadeScale(page) as PageRouteBuilder<T>;
        break;
      case PageTransitionType.rotationFade:
        route = PageTransitions.rotationFade(page) as PageRouteBuilder<T>;
        break;
      case PageTransitionType.slideFromBottom:
        route = PageTransitions.slideFromBottom(page) as PageRouteBuilder<T>;
        break;
    }

    return Navigator.of(context).pushReplacement<T, TO>(route, result: result);
  }

  static Future<T?> showDialog<T>(
    BuildContext context,
    Widget dialog,
  ) {
    return Navigator.of(context).push<T>(
      PageTransitions.dialogTransition(dialog) as PageRouteBuilder<T>,
    );
  }
}

enum PageTransitionType {
  slideFromRight,
  slideFromLeft,
  fadeScale,
  rotationFade,
  slideFromBottom,
}
