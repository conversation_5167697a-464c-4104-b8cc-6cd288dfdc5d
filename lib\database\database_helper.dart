import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'expense_manager.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // جدول الموظفين
    await db.execute('''
      CREATE TABLE employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        position TEXT,
        department TEXT,
        phone_number TEXT,
        email TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول المصروفات
    await db.execute('''
      CREATE TABLE expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        date TEXT NOT NULL,
        description TEXT NOT NULL,
        receipt_image_path TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول السلف
    await db.execute('''
      CREATE TABLE advances (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        received_date TEXT NOT NULL,
        reason TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        paid_amount REAL NOT NULL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id) ON DELETE CASCADE
      )
    ''');

    // جدول دفعات السداد
    await db.execute('''
      CREATE TABLE advance_payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        advance_id INTEGER NOT NULL,
        amount REAL NOT NULL,
        payment_date TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (advance_id) REFERENCES advances (id) ON DELETE CASCADE
      )
    ''');

    // إنشاء فهارس لتحسين الأداء
    await db.execute('CREATE INDEX idx_expenses_date ON expenses(date)');
    await db.execute('CREATE INDEX idx_expenses_category ON expenses(category)');
    await db.execute('CREATE INDEX idx_advances_employee_id ON advances(employee_id)');
    await db.execute('CREATE INDEX idx_advances_status ON advances(status)');
    await db.execute('CREATE INDEX idx_advance_payments_advance_id ON advance_payments(advance_id)');

    // إدراج بيانات تجريبية للموظفين
    await _insertSampleData(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // سيتم تنفيذ ترقيات قاعدة البيانات هنا في المستقبل
  }

  Future<void> _insertSampleData(Database db) async {
    // إدراج موظفين تجريبيين
    final now = DateTime.now().toIso8601String();
    
    await db.insert('employees', {
      'name': 'أحمد محمد',
      'position': 'محاسب',
      'department': 'المالية',
      'phone_number': '01234567890',
      'email': '<EMAIL>',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('employees', {
      'name': 'فاطمة علي',
      'position': 'مطورة برمجيات',
      'department': 'تقنية المعلومات',
      'phone_number': '01234567891',
      'email': '<EMAIL>',
      'created_at': now,
      'updated_at': now,
    });

    await db.insert('employees', {
      'name': 'محمد حسن',
      'position': 'مدير مبيعات',
      'department': 'المبيعات',
      'phone_number': '01234567892',
      'email': '<EMAIL>',
      'created_at': now,
      'updated_at': now,
    });
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // حذف قاعدة البيانات (للاختبار فقط)
  Future<void> deleteDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'expense_manager.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }

  // نسخ احتياطي من قاعدة البيانات
  Future<String> backupDatabase() async {
    final db = await database;
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String sourcePath = db.path;
    String backupPath = join(documentsDirectory.path, 'backup_expense_manager_${DateTime.now().millisecondsSinceEpoch}.db');
    
    File sourceFile = File(sourcePath);
    await sourceFile.copy(backupPath);
    
    return backupPath;
  }

  // استعادة قاعدة البيانات من نسخة احتياطية
  Future<void> restoreDatabase(String backupPath) async {
    await close();
    
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String targetPath = join(documentsDirectory.path, 'expense_manager.db');
    
    File backupFile = File(backupPath);
    await backupFile.copy(targetPath);
    
    _database = null; // إعادة تعيين المتغير لإعادة فتح قاعدة البيانات
  }
}
