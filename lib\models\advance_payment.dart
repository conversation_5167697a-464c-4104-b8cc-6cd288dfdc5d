class AdvancePayment {
  final int? id;
  final int advanceId;
  final double amount;
  final DateTime paymentDate;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  AdvancePayment({
    this.id,
    required this.advanceId,
    required this.amount,
    required this.paymentDate,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // تحويل من Map إلى Object
  factory AdvancePayment.fromMap(Map<String, dynamic> map) {
    return AdvancePayment(
      id: map['id'],
      advanceId: map['advance_id'],
      amount: map['amount']?.toDouble() ?? 0.0,
      paymentDate: DateTime.parse(map['payment_date']),
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'advance_id': advanceId,
      'amount': amount,
      'payment_date': paymentDate.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من دفعة السداد
  AdvancePayment copyWith({
    int? id,
    int? advanceId,
    double? amount,
    DateTime? paymentDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdvancePayment(
      id: id ?? this.id,
      advanceId: advanceId ?? this.advanceId,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'AdvancePayment{id: $id, advanceId: $advanceId, amount: $amount, paymentDate: $paymentDate, notes: $notes}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdvancePayment &&
        other.id == id &&
        other.advanceId == advanceId &&
        other.amount == amount &&
        other.paymentDate == paymentDate &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        advanceId.hashCode ^
        amount.hashCode ^
        paymentDate.hashCode ^
        notes.hashCode;
  }
}
