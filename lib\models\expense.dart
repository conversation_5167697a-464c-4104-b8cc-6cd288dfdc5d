class Expense {
  final int? id;
  final double amount;
  final String category;
  final DateTime date;
  final String description;
  final String? receiptImagePath;
  final DateTime createdAt;
  final DateTime updatedAt;

  Expense({
    this.id,
    required this.amount,
    required this.category,
    required this.date,
    required this.description,
    this.receiptImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // تحويل من Map إلى Object
  factory Expense.fromMap(Map<String, dynamic> map) {
    return Expense(
      id: map['id'],
      amount: map['amount']?.toDouble() ?? 0.0,
      category: map['category'] ?? '',
      date: DateTime.parse(map['date']),
      description: map['description'] ?? '',
      receiptImagePath: map['receipt_image_path'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  // تحويل من Object إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'amount': amount,
      'category': category,
      'date': date.toIso8601String(),
      'description': description,
      'receipt_image_path': receiptImagePath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من المصروف
  Expense copyWith({
    int? id,
    double? amount,
    String? category,
    DateTime? date,
    String? description,
    String? receiptImagePath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Expense(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      date: date ?? this.date,
      description: description ?? this.description,
      receiptImagePath: receiptImagePath ?? this.receiptImagePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'Expense{id: $id, amount: $amount, category: $category, date: $date, description: $description, receiptImagePath: $receiptImagePath}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Expense &&
        other.id == id &&
        other.amount == amount &&
        other.category == category &&
        other.date == date &&
        other.description == description &&
        other.receiptImagePath == receiptImagePath;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        amount.hashCode ^
        category.hashCode ^
        date.hashCode ^
        description.hashCode ^
        receiptImagePath.hashCode;
  }
}

// فئات المصروفات المحددة مسبقاً
class ExpenseCategories {
  static const List<String> categories = [
    'طعام وشراب',
    'مواصلات',
    'مكتب وقرطاسية',
    'اتصالات',
    'صيانة',
    'وقود',
    'ضيافة',
    'أخرى',
  ];

  static const Map<String, String> categoryIcons = {
    'طعام وشراب': '🍽️',
    'مواصلات': '🚗',
    'مكتب وقرطاسية': '📝',
    'اتصالات': '📞',
    'صيانة': '🔧',
    'وقود': '⛽',
    'ضيافة': '☕',
    'أخرى': '📋',
  };
}
