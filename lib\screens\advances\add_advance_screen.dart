import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/advance.dart';
import '../../models/employee.dart';
import '../../services/advance_service.dart';
import '../../services/employee_service.dart';

class AddAdvanceScreen extends StatefulWidget {
  final Advance? advance; // للتعديل

  const AddAdvanceScreen({super.key, this.advance});

  @override
  State<AddAdvanceScreen> createState() => _AddAdvanceScreenState();
}

class _AddAdvanceScreenState extends State<AddAdvanceScreen> {
  final _formKey = GlobalKey<FormState>();
  final AdvanceService _advanceService = AdvanceService();
  final EmployeeService _employeeService = EmployeeService();

  late TextEditingController _amountController;
  late TextEditingController _reasonController;
  List<Employee> _employees = [];
  Employee? _selectedEmployee;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  bool _isLoadingEmployees = true;

  @override
  void initState() {
    super.initState();
    _amountController = TextEditingController();
    _reasonController = TextEditingController();
    _loadEmployees();

    // إذا كان في وضع التعديل، املأ البيانات
    if (widget.advance != null) {
      _amountController.text = widget.advance!.amount.toString();
      _reasonController.text = widget.advance!.reason;
      _selectedDate = widget.advance!.receivedDate;
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  Future<void> _loadEmployees() async {
    try {
      final employees = await _employeeService.getAllEmployees();
      setState(() {
        _employees = employees;
        _isLoadingEmployees = false;

        // إذا كان في وضع التعديل، اختر الموظف المحدد
        if (widget.advance != null) {
          _selectedEmployee = employees.firstWhere(
            (emp) => emp.id == widget.advance!.employeeId,
            orElse: () => employees.first,
          );
        }
      });
    } catch (e) {
      setState(() {
        _isLoadingEmployees = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الموظفين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar', 'SA'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveAdvance() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedEmployee == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار موظف'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final advance = Advance(
        id: widget.advance?.id,
        employeeId: _selectedEmployee!.id!,
        amount: double.parse(_amountController.text),
        receivedDate: _selectedDate,
        reason: _reasonController.text.trim(),
        status: widget.advance?.status ?? AdvanceStatus.pending,
        paidAmount: widget.advance?.paidAmount ?? 0.0,
      );

      if (widget.advance == null) {
        // إضافة سلفة جديدة
        await _advanceService.insertAdvance(advance);
      } else {
        // تحديث سلفة موجودة
        await _advanceService.updateAdvance(advance);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.advance == null
                ? 'تم إضافة السلفة بنجاح'
                : 'تم تحديث السلفة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ السلفة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.advance == null ? 'إضافة سلفة' : 'تعديل سلفة'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _saveAdvance,
              child: const Text('حفظ'),
            ),
        ],
      ),
      body: _isLoadingEmployees
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اختيار الموظف
                    DropdownButtonFormField<Employee>(
                      value: _selectedEmployee,
                      isExpanded: true,
                      decoration: const InputDecoration(
                        labelText: 'الموظف *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      items: _employees.map((employee) {
                        return DropdownMenuItem<Employee>(
                          value: employee,
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                vertical: 8, horizontal: 4),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  employee.name,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF212529),
                                        height: 1.2,
                                      ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                                if (employee.position != null) ...[
                                  const SizedBox(height: 2),
                                  Text(
                                    employee.position!,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: const Color(0xFF6C757D),
                                          fontSize: 12,
                                          height: 1.1,
                                        ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (employee) {
                        setState(() {
                          _selectedEmployee = employee;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'يرجى اختيار موظف';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // حقل المبلغ
                    TextFormField(
                      controller: _amountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'المبلغ *',
                        prefixText: 'ر.س ',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.attach_money),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال المبلغ';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال مبلغ صحيح';
                        }
                        if (double.parse(value) <= 0) {
                          return 'يجب أن يكون المبلغ أكبر من صفر';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // حقل التاريخ
                    InkWell(
                      onTap: _selectDate,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الاستلام *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        child: Text(
                          DateFormat('dd/MM/yyyy').format(_selectedDate),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // حقل السبب
                    TextFormField(
                      controller: _reasonController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'سبب السلفة *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال سبب السلفة';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),

                    // معلومات إضافية للموظف المختار
                    if (_selectedEmployee != null) _buildEmployeeInfoCard(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildEmployeeInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.white],
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person_outline,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات الموظف',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('الاسم', _selectedEmployee!.name),
            if (_selectedEmployee!.position != null)
              _buildInfoRow('المنصب', _selectedEmployee!.position!),
            if (_selectedEmployee!.department != null)
              _buildInfoRow('القسم', _selectedEmployee!.department!),
            if (_selectedEmployee!.phoneNumber != null)
              _buildInfoRow('الهاتف', _selectedEmployee!.phoneNumber!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0),
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.7),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 70,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF495057),
                  ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF212529),
                    height: 1.3,
                  ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
