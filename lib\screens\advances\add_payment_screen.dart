import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/advance.dart';
import '../../models/advance_payment.dart';
import '../../services/advance_service.dart';

class AddPaymentScreen extends StatefulWidget {
  final Advance advance;

  const AddPaymentScreen({super.key, required this.advance});

  @override
  State<AddPaymentScreen> createState() => _AddPaymentScreenState();
}

class _AddPaymentScreenState extends State<AddPaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  final AdvanceService _advanceService = AdvanceService();

  late TextEditingController _amountController;
  late TextEditingController _notesController;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _amountController = TextEditingController();
    _notesController = TextEditingController();
    
    // تعيين المبلغ المتبقي كقيمة افتراضية
    if (widget.advance.remainingAmount > 0) {
      _amountController.text = widget.advance.remainingAmount.toString();
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: widget.advance.receivedDate,
      lastDate: DateTime.now(),
      locale: const Locale('ar', 'SA'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _savePayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.parse(_amountController.text);
    
    // التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
    if (amount > widget.advance.remainingAmount) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'المبلغ المدخل (${NumberFormat.currency(locale: 'ar_SA', symbol: 'ر.س').format(amount)}) '
            'يتجاوز المبلغ المتبقي (${NumberFormat.currency(locale: 'ar_SA', symbol: 'ر.س').format(widget.advance.remainingAmount)})'
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final payment = AdvancePayment(
        advanceId: widget.advance.id!,
        amount: amount,
        paymentDate: _selectedDate,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      await _advanceService.addPayment(payment);

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة الدفعة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الدفعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة دفعة سداد'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _savePayment,
              child: const Text('حفظ'),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات السلفة
              _buildAdvanceInfoCard(),
              const SizedBox(height: 16),

              // حقل المبلغ
              TextFormField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'مبلغ الدفعة *',
                  prefixText: 'ر.س ',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال مبلغ الدفعة';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال مبلغ صحيح';
                  }
                  if (double.parse(value) <= 0) {
                    return 'يجب أن يكون المبلغ أكبر من صفر';
                  }
                  if (double.parse(value) > widget.advance.remainingAmount) {
                    return 'المبلغ يتجاوز المبلغ المتبقي';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // حقل التاريخ
              InkWell(
                onTap: _selectDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ الدفعة *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    DateFormat('dd/MM/yyyy').format(_selectedDate),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // حقل الملاحظات
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                ),
              ),
              const SizedBox(height: 24),

              // أزرار سريعة للمبالغ
              _buildQuickAmountButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdvanceInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات السلفة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('الموظف', widget.advance.employee?.name ?? 'غير معروف'),
            _buildInfoRow('السبب', widget.advance.reason),
            _buildInfoRow(
              'مبلغ السلفة',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'ر.س',
                decimalDigits: 2,
              ).format(widget.advance.amount),
            ),
            _buildInfoRow(
              'المبلغ المدفوع',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'ر.س',
                decimalDigits: 2,
              ).format(widget.advance.paidAmount),
            ),
            _buildInfoRow(
              'المبلغ المتبقي',
              NumberFormat.currency(
                locale: 'ar_SA',
                symbol: 'ر.س',
                decimalDigits: 2,
              ).format(widget.advance.remainingAmount),
              valueColor: Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor,
                fontWeight: valueColor != null ? FontWeight.bold : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAmountButtons() {
    final remainingAmount = widget.advance.remainingAmount;
    
    if (remainingAmount <= 0) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مبالغ سريعة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            // المبلغ الكامل
            _buildQuickAmountButton(
              'المبلغ الكامل',
              remainingAmount,
            ),
            // نصف المبلغ
            if (remainingAmount >= 2)
              _buildQuickAmountButton(
                'نصف المبلغ',
                remainingAmount / 2,
              ),
            // ربع المبلغ
            if (remainingAmount >= 4)
              _buildQuickAmountButton(
                'ربع المبلغ',
                remainingAmount / 4,
              ),
            // مبالغ ثابتة
            ...[100, 500, 1000, 5000].where((amount) => amount <= remainingAmount).map(
              (amount) => _buildQuickAmountButton(
                '${amount.toString()} ر.س',
                amount.toDouble(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickAmountButton(String label, double amount) {
    return OutlinedButton(
      onPressed: () {
        _amountController.text = amount.toString();
      },
      child: Text(label),
    );
  }
}
