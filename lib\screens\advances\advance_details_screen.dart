import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/advance.dart';
import '../../models/advance_payment.dart';
import '../../services/advance_service.dart';
import 'add_advance_screen.dart';
import 'add_payment_screen.dart';

class AdvanceDetailsScreen extends StatefulWidget {
  final Advance advance;

  const AdvanceDetailsScreen({super.key, required this.advance});

  @override
  State<AdvanceDetailsScreen> createState() => _AdvanceDetailsScreenState();
}

class _AdvanceDetailsScreenState extends State<AdvanceDetailsScreen> {
  final AdvanceService _advanceService = AdvanceService();
  late Advance _advance;
  List<AdvancePayment> _payments = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _advance = widget.advance;
    _loadPayments();
  }

  Future<void> _loadPayments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final payments = await _advanceService.getPaymentsByAdvance(_advance.id!);
      final updatedAdvance = await _advanceService.getAdvanceById(_advance.id!);
      
      setState(() {
        _payments = payments;
        if (updatedAdvance != null) {
          _advance = updatedAdvance;
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteAdvance() async {
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف سلفة "${_advance.employee?.name ?? 'موظف غير معروف'}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _advanceService.deleteAdvance(_advance.id!);
        if (mounted) {
          Navigator.pop(context, true);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف السلفة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف السلفة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _editAdvance() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddAdvanceScreen(advance: _advance),
      ),
    );

    if (result == true) {
      _loadPayments(); // إعادة تحميل البيانات
    }
  }

  Future<void> _addPayment() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddPaymentScreen(advance: _advance),
      ),
    );

    if (result == true) {
      _loadPayments(); // إعادة تحميل البيانات
    }
  }

  Color _getStatusColor() {
    switch (_advance.status) {
      case AdvanceStatus.pending:
        return Colors.orange;
      case AdvanceStatus.partiallyPaid:
        return Colors.blue;
      case AdvanceStatus.fullyPaid:
        return Colors.green;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل السلفة'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_advance.status != AdvanceStatus.fullyPaid)
            IconButton(
              icon: const Icon(Icons.payment),
              onPressed: _addPayment,
              tooltip: 'إضافة دفعة',
            ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editAdvance,
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteAdvance,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // بطاقة المعلومات الأساسية
                  _buildBasicInfoCard(),
                  const SizedBox(height: 16),
                  
                  // بطاقة معلومات الموظف
                  _buildEmployeeInfoCard(),
                  const SizedBox(height: 16),
                  
                  // بطاقة حالة السداد
                  _buildPaymentStatusCard(),
                  const SizedBox(height: 16),
                  
                  // قائمة الدفعات
                  _buildPaymentsSection(),
                ],
              ),
            ),
      floatingActionButton: _advance.status != AdvanceStatus.fullyPaid
          ? FloatingActionButton(
              onPressed: _addPayment,
              child: const Icon(Icons.add),
              tooltip: 'إضافة دفعة',
            )
          : null,
    );
  }

  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(),
                  radius: 30,
                  child: Icon(
                    _advance.status == AdvanceStatus.fullyPaid
                        ? Icons.check_circle
                        : _advance.status == AdvanceStatus.partiallyPaid
                            ? Icons.payments
                            : Icons.pending,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _advance.reason,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getStatusColor().withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _advance.status.arabicName,
                          style: TextStyle(
                            color: _getStatusColor(),
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    'مبلغ السلفة',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    NumberFormat.currency(
                      locale: 'ar_SA',
                      symbol: 'ر.س',
                      decimalDigits: 2,
                    ).format(_advance.amount),
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeInfoCard() {
    if (_advance.employee == null) {
      return const SizedBox();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الموظف',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('الاسم', _advance.employee!.name),
            if (_advance.employee!.position != null)
              _buildDetailRow('المنصب', _advance.employee!.position!),
            if (_advance.employee!.department != null)
              _buildDetailRow('القسم', _advance.employee!.department!),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة السداد',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    'المبلغ المدفوع',
                    NumberFormat.currency(
                      locale: 'ar_SA',
                      symbol: 'ر.س',
                      decimalDigits: 2,
                    ).format(_advance.paidAmount),
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatusItem(
                    'المبلغ المتبقي',
                    NumberFormat.currency(
                      locale: 'ar_SA',
                      symbol: 'ر.س',
                      decimalDigits: 2,
                    ).format(_advance.remainingAmount),
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: _advance.amount > 0 ? _advance.paidAmount / _advance.amount : 0,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor()),
            ),
            const SizedBox(height: 8),
            Text(
              'نسبة السداد: ${(_advance.amount > 0 ? (_advance.paidAmount / _advance.amount * 100) : 0).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'سجل الدفعات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_payments.length} دفعة',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_payments.isEmpty)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.payment,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد دفعات',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _payments.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final payment = _payments[index];
                  return _buildPaymentItem(payment);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentItem(AdvancePayment payment) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: CircleAvatar(
        backgroundColor: Colors.green,
        child: const Icon(Icons.payment, color: Colors.white),
      ),
      title: Text(
        NumberFormat.currency(
          locale: 'ar_SA',
          symbol: 'ر.س',
          decimalDigits: 2,
        ).format(payment.amount),
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(DateFormat('dd/MM/yyyy').format(payment.paymentDate)),
          if (payment.notes != null && payment.notes!.isNotEmpty)
            Text(
              payment.notes!,
              style: TextStyle(color: Colors.grey[600]),
            ),
        ],
      ),
      trailing: IconButton(
        icon: const Icon(Icons.delete, color: Colors.red),
        onPressed: () => _deletePayment(payment),
      ),
    );
  }

  Future<void> _deletePayment(AdvancePayment payment) async {
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه الدفعة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await _advanceService.deletePayment(payment.id!);
        _loadPayments();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الدفعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الدفعة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
