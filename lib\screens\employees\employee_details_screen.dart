import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/employee.dart';
import '../../models/advance.dart';
import '../../services/employee_service.dart';
import '../../services/advance_service.dart';
import '../../utils/currency_helper.dart';
import '../../utils/responsive_helper.dart';
import '../../animations/page_transitions.dart';
import '../../animations/loading_animations.dart';
import '../../theme/app_theme.dart';
import 'edit_employee_screen.dart';
import '../advances/add_advance_screen.dart';

class EmployeeDetailsScreen extends StatefulWidget {
  final Employee employee;

  const EmployeeDetailsScreen({
    super.key,
    required this.employee,
  });

  @override
  State<EmployeeDetailsScreen> createState() => _EmployeeDetailsScreenState();
}

class _EmployeeDetailsScreenState extends State<EmployeeDetailsScreen> {
  final EmployeeService _employeeService = EmployeeService();
  final AdvanceService _advanceService = AdvanceService();

  List<Advance> _employeeAdvances = [];
  bool _isLoading = true;
  double _totalAdvances = 0;
  double _totalPaid = 0;
  double _totalRemaining = 0;

  @override
  void initState() {
    super.initState();
    _loadEmployeeData();
  }

  Future<void> _loadEmployeeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final advances =
          await _advanceService.getAdvancesByEmployee(widget.employee.id!);

      double totalAdvances = 0;
      double totalPaid = 0;
      double totalRemaining = 0;

      for (final advance in advances) {
        totalAdvances += advance.amount;
        totalPaid += advance.paidAmount;
        totalRemaining += advance.remainingAmount;
      }

      setState(() {
        _employeeAdvances = advances;
        _totalAdvances = totalAdvances;
        _totalPaid = totalPaid;
        _totalRemaining = totalRemaining;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات الموظف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteEmployee() async {
    if (_totalRemaining > 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن حذف الموظف لوجود سلف مستحقة عليه'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف "${widget.employee.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _employeeService.deleteEmployee(widget.employee.id!);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الموظف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الموظف: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: CustomScrollView(
          slivers: [
            // AppBar مع تدرج
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: Colors.transparent,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  widget.employee.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                  ),
                ),
              ),
              actions: [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        AnimatedNavigator.push(
                          context,
                          EditEmployeeScreen(employee: widget.employee),
                          type: PageTransitionType.slideFromRight,
                        ).then((_) => _loadEmployeeData());
                        break;
                      case 'delete':
                        _deleteEmployee();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem<String>(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            // المحتوى الرئيسي
            SliverToBoxAdapter(
              child: _isLoading
                  ? Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: LoadingAnimations.summaryShimmer(),
                    )
                  : Padding(
                      padding: ResponsiveHelper.getPadding(context),
                      child: AnimationLimiter(
                        child: Column(
                          children: AnimationConfiguration.toStaggeredList(
                            duration: const Duration(milliseconds: 375),
                            childAnimationBuilder: (widget) => SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: widget,
                              ),
                            ),
                            children: [
                              const SizedBox(height: 20),

                              // بطاقة معلومات الموظف
                              _buildEmployeeInfoCard(),
                              const SizedBox(height: 16),

                              // بطاقات الإحصائيات
                              _buildStatisticsCards(),
                              const SizedBox(height: 16),

                              // قائمة السلف
                              _buildAdvancesList(),
                              const SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          AnimatedNavigator.push(
            context,
            const AddAdvanceScreen(),
            type: PageTransitionType.slideFromBottom,
          ).then((_) => _loadEmployeeData());
        },
        icon: const Icon(Icons.add),
        label: const Text('إضافة سلفة'),
        backgroundColor: AppTheme.secondaryColor,
      ),
    );
  }

  Widget _buildEmployeeInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            // أيقونة الموظف
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 16),

            // اسم الموظف
            Text(
              widget.employee.name,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),

            if (widget.employee.position != null) ...[
              const SizedBox(height: 8),
              Text(
                widget.employee.position!,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                textAlign: TextAlign.center,
              ),
            ],

            const SizedBox(height: 16),

            // معلومات إضافية
            _buildInfoRow(
              icon: Icons.business,
              label: 'القسم',
              value: widget.employee.department ?? 'غير محدد',
            ),

            if (widget.employee.phoneNumber != null)
              _buildInfoRow(
                icon: Icons.phone,
                label: 'الهاتف',
                value: widget.employee.phoneNumber!,
              ),

            if (widget.employee.email != null)
              _buildInfoRow(
                icon: Icons.email,
                label: 'البريد الإلكتروني',
                value: widget.employee.email!,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return ResponsiveGrid(
      mobileColumns: 1,
      tabletColumns: 3,
      desktopColumns: 3,
      spacing: 12,
      runSpacing: 12,
      children: [
        _buildStatCard(
          title: 'إجمالي السلف',
          value: CurrencyHelper.formatCurrency(_totalAdvances),
          icon: Icons.account_balance_wallet,
          color: AppTheme.primaryColor,
        ),
        _buildStatCard(
          title: 'المدفوع',
          value: CurrencyHelper.formatCurrency(_totalPaid),
          icon: Icons.payment,
          color: AppTheme.successColor,
        ),
        _buildStatCard(
          title: 'المتبقي',
          value: CurrencyHelper.formatCurrency(_totalRemaining),
          icon: Icons.pending_actions,
          color: _totalRemaining > 0
              ? AppTheme.warningColor
              : AppTheme.successColor,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancesList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.list_alt,
                    color: AppTheme.secondaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'سلف الموظف',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.secondaryColor,
                      ),
                ),
                const Spacer(),
                Text(
                  '(${_employeeAdvances.length})',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_employeeAdvances.isEmpty)
              Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.account_balance_wallet_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد سلف لهذا الموظف',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اضغط على زر "إضافة سلفة" لإضافة سلفة جديدة',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[500],
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _employeeAdvances.length,
                itemBuilder: (context, index) {
                  final advance = _employeeAdvances[index];
                  return _buildAdvanceItem(advance);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvanceItem(Advance advance) {
    final isFullyPaid = advance.remainingAmount <= 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isFullyPaid ? AppTheme.successColor : AppTheme.warningColor,
          width: 1,
        ),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (isFullyPaid ? AppTheme.successColor : AppTheme.warningColor)
                .withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isFullyPaid ? Icons.check_circle : Icons.pending,
            color: isFullyPaid ? AppTheme.successColor : AppTheme.warningColor,
            size: 20,
          ),
        ),
        title: Text(
          CurrencyHelper.formatCurrency(advance.amount),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (advance.reason.isNotEmpty) Text(advance.reason),
            Text(
              'متبقي: ${CurrencyHelper.formatCurrency(advance.remainingAmount)}',
              style: TextStyle(
                color:
                    isFullyPaid ? AppTheme.successColor : AppTheme.warningColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isFullyPaid ? AppTheme.successColor : AppTheme.warningColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            isFullyPaid ? 'مسددة' : 'مستحقة',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
