import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../models/employee.dart';
import '../../services/employee_service.dart';
import '../../utils/responsive_helper.dart';
import '../../animations/page_transitions.dart';
import '../../animations/loading_animations.dart';
import '../../theme/app_theme.dart';
import 'add_employee_screen.dart';
import 'edit_employee_screen.dart';
import 'employee_details_screen.dart';

class EmployeesScreen extends StatefulWidget {
  const EmployeesScreen({super.key});

  @override
  State<EmployeesScreen> createState() => _EmployeesScreenState();
}

class _EmployeesScreenState extends State<EmployeesScreen> {
  final EmployeeService _employeeService = EmployeeService();
  List<Employee> _employees = [];
  List<Employee> _filteredEmployees = [];
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  String _selectedDepartment = '';
  String _selectedPosition = '';

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final employees = await _employeeService.getAllEmployees();
      setState(() {
        _employees = employees;
        _filteredEmployees = employees;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الموظفين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterEmployees() {
    setState(() {
      _filteredEmployees = _employees.where((employee) {
        final matchesSearch = _searchController.text.isEmpty ||
            employee.name
                .toLowerCase()
                .contains(_searchController.text.toLowerCase()) ||
            (employee.position
                    ?.toLowerCase()
                    .contains(_searchController.text.toLowerCase()) ??
                false) ||
            (employee.department
                    ?.toLowerCase()
                    .contains(_searchController.text.toLowerCase()) ??
                false);

        final matchesDepartment = _selectedDepartment.isEmpty ||
            employee.department == _selectedDepartment;

        final matchesPosition =
            _selectedPosition.isEmpty || employee.position == _selectedPosition;

        return matchesSearch && matchesDepartment && matchesPosition;
      }).toList();
    });
  }

  List<String> _getUniqueDepartments() {
    return _employees
        .where((e) => e.department != null && e.department!.isNotEmpty)
        .map((e) => e.department!)
        .toSet()
        .toList()
      ..sort();
  }

  List<String> _getUniquePositions() {
    return _employees
        .where((e) => e.position != null && e.position!.isNotEmpty)
        .map((e) => e.position!)
        .toSet()
        .toList()
      ..sort();
  }

  Future<void> _deleteEmployee(Employee employee) async {
    // التحقق من وجود سلف مستحقة
    final hasOutstanding =
        await _employeeService.hasOutstandingAdvances(employee.id!);

    if (hasOutstanding) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يمكن حذف الموظف لوجود سلف مستحقة عليه'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف "${employee.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _employeeService.deleteEmployee(employee.id!);
        _loadEmployees();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الموظف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الموظف: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: CustomScrollView(
          slivers: [
            // AppBar مع تدرج
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: Colors.transparent,
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'إدارة الموظفين',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                  ),
                ),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: _loadEmployees,
                ),
              ],
            ),

            // المحتوى الرئيسي
            SliverToBoxAdapter(
              child: _isLoading
                  ? Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: LoadingAnimations.cardListShimmer(),
                    )
                  : Padding(
                      padding: ResponsiveHelper.getPadding(context),
                      child: Column(
                        children: [
                          const SizedBox(height: 20),
                          // شريط البحث والفلاتر
                          _buildSearchAndFilters(),
                          const SizedBox(height: 20),
                          // قائمة الموظفين
                          _buildEmployeesList(),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          AnimatedNavigator.push(
            context,
            const AddEmployeeScreen(),
            type: PageTransitionType.slideFromBottom,
          ).then((_) => _loadEmployees());
        },
        icon: const Icon(Icons.person_add),
        label: const Text('إضافة موظف'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // شريط البحث
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: 'البحث في الموظفين',
                hintText: 'ابحث بالاسم أو المنصب أو القسم',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (_) => _filterEmployees(),
            ),
            const SizedBox(height: 16),
            // فلاتر القسم والمنصب
            ResponsiveWidget(
              mobile: Column(
                children: [
                  _buildDepartmentFilter(),
                  const SizedBox(height: 12),
                  _buildPositionFilter(),
                ],
              ),
              tablet: Row(
                children: [
                  Expanded(child: _buildDepartmentFilter()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildPositionFilter()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDepartmentFilter() {
    final departments = _getUniqueDepartments();
    return DropdownButtonFormField<String>(
      value: _selectedDepartment.isEmpty ? null : _selectedDepartment,
      decoration: const InputDecoration(
        labelText: 'فلترة حسب القسم',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.business),
      ),
      items: [
        const DropdownMenuItem<String>(
          value: null,
          child: Text('جميع الأقسام'),
        ),
        ...departments.map((dept) => DropdownMenuItem<String>(
              value: dept,
              child: Text(dept),
            )),
      ],
      onChanged: (value) {
        setState(() {
          _selectedDepartment = value ?? '';
        });
        _filterEmployees();
      },
    );
  }

  Widget _buildPositionFilter() {
    final positions = _getUniquePositions();
    return DropdownButtonFormField<String>(
      value: _selectedPosition.isEmpty ? null : _selectedPosition,
      decoration: const InputDecoration(
        labelText: 'فلترة حسب المنصب',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.work),
      ),
      items: [
        const DropdownMenuItem<String>(
          value: null,
          child: Text('جميع المناصب'),
        ),
        ...positions.map((pos) => DropdownMenuItem<String>(
              value: pos,
              child: Text(pos),
            )),
      ],
      onChanged: (value) {
        setState(() {
          _selectedPosition = value ?? '';
        });
        _filterEmployees();
      },
    );
  }

  Widget _buildEmployeesList() {
    if (_filteredEmployees.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            children: [
              Icon(
                Icons.people_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                _employees.isEmpty ? 'لا يوجد موظفين' : 'لا توجد نتائج للبحث',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              if (_employees.isEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  'اضغط على زر "إضافة موظف" لإضافة موظف جديد',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[500],
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _filteredEmployees.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 12.0),
                  child: _buildEmployeeCard(_filteredEmployees[index]),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmployeeCard(Employee employee) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            AnimatedNavigator.push(
              context,
              EmployeeDetailsScreen(employee: employee),
              type: PageTransitionType.fadeScale,
            ).then((_) => _loadEmployees());
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: ResponsiveHelper.getPadding(
              context,
              mobile: const EdgeInsets.all(16),
              tablet: const EdgeInsets.all(20),
            ),
            child: Row(
              children: [
                // أيقونة الموظف
                Container(
                  width: ResponsiveHelper.getIconSize(
                    context,
                    mobile: 50,
                    tablet: 60,
                  ),
                  height: ResponsiveHelper.getIconSize(
                    context,
                    mobile: 50,
                    tablet: 60,
                  ),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.person,
                    color: Colors.white,
                    size: ResponsiveHelper.getIconSize(
                      context,
                      mobile: 28,
                      tablet: 32,
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // معلومات الموظف
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.name,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  fontSize: ResponsiveHelper.getFontSize(
                                    context,
                                    mobile: 16,
                                    tablet: 18,
                                  ),
                                ),
                      ),
                      if (employee.position != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          employee.position!,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppTheme.primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                      ],
                      if (employee.department != null) ...[
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Icon(
                              Icons.business,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              employee.department!,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                            ),
                          ],
                        ),
                      ],
                      if (employee.phoneNumber != null) ...[
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Icon(
                              Icons.phone,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              employee.phoneNumber!,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),

                // أزرار الإجراءات
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        AnimatedNavigator.push(
                          context,
                          EditEmployeeScreen(employee: employee),
                          type: PageTransitionType.slideFromRight,
                        ).then((_) => _loadEmployees());
                        break;
                      case 'delete':
                        _deleteEmployee(employee);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem<String>(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
