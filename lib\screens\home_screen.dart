import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../services/expense_service.dart';
import '../services/advance_service.dart';
import '../utils/currency_helper.dart';
import '../utils/responsive_helper.dart';
import '../animations/page_transitions.dart';
import '../animations/loading_animations.dart';
import '../theme/app_theme.dart';
import 'expenses/expenses_screen.dart';
import 'advances/advances_screen.dart';
import 'employees/employees_screen.dart';
import 'reports/reports_screen.dart';
import 'settings/settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ExpenseService _expenseService = ExpenseService();
  final AdvanceService _advanceService = AdvanceService();

  double _todayExpenses = 0.0;
  double _monthExpenses = 0.0;
  double _totalOutstandingAdvances = 0.0;
  int _outstandingAdvancesCount = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));
      final monthStart = DateTime(now.year, now.month, 1);
      final monthEnd = DateTime(now.year, now.month + 1, 0);

      // حساب مصروفات اليوم
      final todayExpenses = await _expenseService.getTotalExpenses(
        startDate: today,
        endDate: tomorrow,
      );

      // حساب مصروفات الشهر
      final monthExpenses = await _expenseService.getTotalExpenses(
        startDate: monthStart,
        endDate: monthEnd,
      );

      // حساب السلف المستحقة
      final outstandingAdvances =
          await _advanceService.getOutstandingAdvances();
      final totalOutstanding = outstandingAdvances.fold<double>(
        0.0,
        (sum, advance) => sum + advance.remainingAmount,
      );

      setState(() {
        _todayExpenses = todayExpenses;
        _monthExpenses = monthExpenses;
        _totalOutstandingAdvances = totalOutstanding;
        _outstandingAdvancesCount = outstandingAdvances.length;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: CustomScrollView(
          slivers: [
            // AppBar مع تدرج
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: Colors.transparent,
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'مدير المصروفات والسلف',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                  ),
                ),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: _loadDashboardData,
                ),
              ],
            ),

            // المحتوى الرئيسي
            SliverToBoxAdapter(
              child: _isLoading
                  ? Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: LoadingAnimations.summaryShimmer(),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadDashboardData,
                      child: Padding(
                        padding: ResponsiveHelper.getPadding(context),
                        child: AnimationLimiter(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: AnimationConfiguration.toStaggeredList(
                              duration: const Duration(milliseconds: 375),
                              childAnimationBuilder: (widget) => SlideAnimation(
                                verticalOffset: 50.0,
                                child: FadeInAnimation(
                                  child: widget,
                                ),
                              ),
                              children: [
                                const SizedBox(height: 20),
                                // بطاقات الملخص
                                _buildSummaryCards(),
                                const SizedBox(height: 24),

                                // الإجراءات السريعة
                                _buildQuickActions(),
                                const SizedBox(height: 24),

                                // الأقسام الرئيسية
                                _buildMainSections(),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص اليوم',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                title: 'مصروفات اليوم',
                value: CurrencyHelper.formatCurrency(_todayExpenses),
                icon: Icons.today,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                title: 'مصروفات الشهر',
                value: CurrencyHelper.formatCurrency(_monthExpenses),
                icon: Icons.calendar_month,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                title: 'السلف المستحقة',
                value: CurrencyHelper.formatCurrency(_totalOutstandingAdvances),
                icon: Icons.account_balance_wallet,
                color: Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                title: 'عدد السلف المستحقة',
                value: _outstandingAdvancesCount.toString(),
                icon: Icons.people,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: ResponsiveHelper.getPadding(
          context,
          mobile: const EdgeInsets.all(20),
          tablet: const EdgeInsets.all(24),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: ResponsiveHelper.getIconSize(context),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                    fontSize: ResponsiveHelper.getFontSize(
                      context,
                      mobile: 18,
                      tablet: 20,
                      desktop: 22,
                    ),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        ResponsiveWidget(
          mobile: Column(
            children: [
              _buildQuickActionButton(
                title: 'إضافة مصروف',
                icon: Icons.add,
                gradient: AppTheme.primaryGradient,
                onPressed: () {
                  AnimatedNavigator.push(
                    context,
                    const ExpensesScreen(),
                    type: PageTransitionType.slideFromRight,
                  ).then((_) => _loadDashboardData());
                },
              ),
              const SizedBox(height: 12),
              _buildQuickActionButton(
                title: 'إضافة سلفة',
                icon: Icons.person_add,
                gradient: AppTheme.secondaryGradient,
                onPressed: () {
                  AnimatedNavigator.push(
                    context,
                    const AdvancesScreen(),
                    type: PageTransitionType.slideFromRight,
                  ).then((_) => _loadDashboardData());
                },
              ),
            ],
          ),
          tablet: Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  title: 'إضافة مصروف',
                  icon: Icons.add,
                  gradient: AppTheme.primaryGradient,
                  onPressed: () {
                    AnimatedNavigator.push(
                      context,
                      const ExpensesScreen(),
                      type: PageTransitionType.slideFromRight,
                    ).then((_) => _loadDashboardData());
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQuickActionButton(
                  title: 'إضافة سلفة',
                  icon: Icons.person_add,
                  gradient: AppTheme.secondaryGradient,
                  onPressed: () {
                    AnimatedNavigator.push(
                      context,
                      const AdvancesScreen(),
                      type: PageTransitionType.slideFromRight,
                    ).then((_) => _loadDashboardData());
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionButton({
    required String title,
    required IconData icon,
    required LinearGradient gradient,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: double.infinity,
      height: ResponsiveHelper.getHeight(
        context,
        mobile: 60,
        tablet: 70,
      ),
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: gradient.colors.first.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: ResponsiveHelper.getIconSize(context),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: ResponsiveHelper.getFontSize(
                          context,
                          mobile: 16,
                          tablet: 18,
                        ),
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainSections() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأقسام الرئيسية',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 2,
          desktopColumns: 4,
          spacing: ResponsiveHelper.getSpacing(context),
          runSpacing: ResponsiveHelper.getSpacing(context),
          children: [
            _buildSectionCard(
              title: 'المصروفات',
              subtitle: 'إدارة المصروفات اليومية',
              icon: Icons.receipt_long,
              color: Colors.blue,
              onTap: () {},
            ),
            _buildSectionCard(
              title: 'السلف',
              subtitle: 'إدارة سلف الموظفين',
              icon: Icons.account_balance_wallet,
              color: Colors.green,
              onTap: () {},
            ),
            _buildSectionCard(
              title: 'الموظفين',
              subtitle: 'إدارة بيانات الموظفين',
              icon: Icons.people,
              color: Colors.purple,
              onTap: () {},
            ),
            _buildSectionCard(
              title: 'التقارير',
              subtitle: 'التقارير والإحصائيات',
              icon: Icons.analytics,
              color: Colors.orange,
              onTap: () {},
            ),
            _buildSectionCard(
              title: 'الإعدادات',
              subtitle: 'إعدادات التطبيق',
              icon: Icons.settings,
              color: Colors.grey,
              onTap: () {},
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            AnimatedNavigator.push(
              context,
              _getScreenForSection(title),
              type: PageTransitionType.fadeScale,
            ).then((_) => _loadDashboardData());
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: ResponsiveHelper.getPadding(
              context,
              mobile: const EdgeInsets.all(12),
              tablet: const EdgeInsets.all(16),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: ResponsiveHelper.getIconSize(
                      context,
                      mobile: 20,
                      tablet: 24,
                      desktop: 28,
                    ),
                    color: color,
                  ),
                ),
                const SizedBox(height: 8),
                Flexible(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: ResponsiveHelper.getFontSize(
                            context,
                            mobile: 14,
                            tablet: 16,
                          ),
                        ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 2),
                Flexible(
                  child: Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                          fontSize: ResponsiveHelper.getFontSize(
                            context,
                            mobile: 10,
                            tablet: 12,
                          ),
                        ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _getScreenForSection(String title) {
    switch (title) {
      case 'المصروفات':
        return const ExpensesScreen();
      case 'السلف':
        return const AdvancesScreen();
      case 'الموظفين':
        return const EmployeesScreen();
      case 'التقارير':
        return const ReportsScreen();
      case 'الإعدادات':
        return const SettingsScreen();
      default:
        return const ExpensesScreen();
    }
  }
}
