import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../services/expense_service.dart';
import '../../services/advance_service.dart';
import '../../models/expense.dart';
import '../../models/advance.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with SingleTickerProviderStateMixin {
  final ExpenseService _expenseService = ExpenseService();
  final AdvanceService _advanceService = AdvanceService();
  late TabController _tabController;

  // بيانات المصروفات
  Map<String, double> _expensesByCategory = {};
  Map<String, double> _dailyExpenses = {};
  double _totalExpensesThisMonth = 0.0;
  double _totalExpensesLastMonth = 0.0;

  // بيانات السلف
  Map<String, dynamic> _advanceStatistics = {};

  bool _isLoading = true;
  DateTime _selectedMonth = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadReportsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReportsData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Future.wait([
        _loadExpenseReports(),
        _loadAdvanceReports(),
      ]);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التقارير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadExpenseReports() async {
    final now = DateTime.now();
    final thisMonthStart = DateTime(now.year, now.month, 1);
    final thisMonthEnd = DateTime(now.year, now.month + 1, 0);
    final lastMonthStart = DateTime(now.year, now.month - 1, 1);
    final lastMonthEnd = DateTime(now.year, now.month, 0);

    // إجمالي مصروفات هذا الشهر
    _totalExpensesThisMonth = await _expenseService.getTotalExpenses(
      startDate: thisMonthStart,
      endDate: thisMonthEnd,
    );

    // إجمالي مصروفات الشهر الماضي
    _totalExpensesLastMonth = await _expenseService.getTotalExpenses(
      startDate: lastMonthStart,
      endDate: lastMonthEnd,
    );

    // المصروفات حسب الفئة
    _expensesByCategory = await _expenseService.getExpensesByCategories(
      startDate: thisMonthStart,
      endDate: thisMonthEnd,
    );

    // المصروفات اليومية للأسبوع الماضي
    _dailyExpenses = await _expenseService.getDailyExpensesForWeek();
  }

  Future<void> _loadAdvanceReports() async {
    _advanceStatistics = await _advanceService.getAdvanceStatistics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير والإحصائيات'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReportsData,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReports,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المصروفات'),
            Tab(text: 'السلف'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildExpenseReports(),
                _buildAdvanceReports(),
              ],
            ),
    );
  }

  Widget _buildExpenseReports() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص المصروفات
          _buildExpenseSummaryCards(),
          const SizedBox(height: 24),

          // قائمة المصروفات حسب الفئة
          _buildExpensesByCategoryList(),
          const SizedBox(height: 24),

          // قائمة المصروفات اليومية
          _buildDailyExpensesList(),
        ],
      ),
    );
  }

  Widget _buildAdvanceReports() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص السلف
          _buildAdvanceSummaryCards(),
          const SizedBox(height: 24),

          // قائمة حالات السلف
          _buildAdvanceStatusList(),
        ],
      ),
    );
  }

  Widget _buildExpenseSummaryCards() {
    final changePercentage = _totalExpensesLastMonth > 0
        ? ((_totalExpensesThisMonth - _totalExpensesLastMonth) /
            _totalExpensesLastMonth *
            100)
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص المصروفات',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                title: 'هذا الشهر',
                value: NumberFormat.currency(
                  locale: 'ar_SA',
                  symbol: 'ر.س',
                  decimalDigits: 2,
                ).format(_totalExpensesThisMonth),
                icon: Icons.calendar_month,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                title: 'الشهر الماضي',
                value: NumberFormat.currency(
                  locale: 'ar_SA',
                  symbol: 'ر.س',
                  decimalDigits: 2,
                ).format(_totalExpensesLastMonth),
                icon: Icons.history,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  changePercentage >= 0
                      ? Icons.trending_up
                      : Icons.trending_down,
                  color: changePercentage >= 0 ? Colors.red : Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  'التغيير: ${changePercentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: changePercentage >= 0 ? Colors.red : Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvanceSummaryCards() {
    final stats = _advanceStatistics;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص السلف',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                title: 'إجمالي السلف',
                value: NumberFormat.currency(
                  locale: 'ar_SA',
                  symbol: 'ر.س',
                  decimalDigits: 2,
                ).format(stats['totalAdvances'] ?? 0.0),
                icon: Icons.account_balance_wallet,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                title: 'المبلغ المدفوع',
                value: NumberFormat.currency(
                  locale: 'ar_SA',
                  symbol: 'ر.س',
                  decimalDigits: 2,
                ).format(stats['totalPaid'] ?? 0.0),
                icon: Icons.payment,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildSummaryCard(
          title: 'المبلغ المستحق',
          value: NumberFormat.currency(
            locale: 'ar_SA',
            symbol: 'ر.س',
            decimalDigits: 2,
          ).format(stats['outstanding'] ?? 0.0),
          icon: Icons.pending_actions,
          color: Colors.orange,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpensesByCategoryList() {
    if (_expensesByCategory.isEmpty) {
      return _buildEmptyChart('لا توجد مصروفات لعرضها');
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المصروفات حسب الفئة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _expensesByCategory.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final entry = _expensesByCategory.entries.elementAt(index);
                final total =
                    _expensesByCategory.values.reduce((a, b) => a + b);
                final percentage = (entry.value / total * 100);
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getCategoryColor(index),
                    child: Text(
                      ExpenseCategories.categoryIcons[entry.key] ?? '📋',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  title: Text(entry.key),
                  subtitle: Text('${percentage.toStringAsFixed(1)}%'),
                  trailing: Text(
                    NumberFormat.currency(
                      locale: 'ar_SA',
                      symbol: 'ر.س',
                      decimalDigits: 2,
                    ).format(entry.value),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyExpensesList() {
    if (_dailyExpenses.isEmpty) {
      return _buildEmptyChart('لا توجد مصروفات يومية لعرضها');
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المصروفات اليومية (آخر 7 أيام)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _dailyExpenses.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final entry = _dailyExpenses.entries.elementAt(index);
                final date = DateTime.parse(entry.key);
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue,
                    child: Text(
                      DateFormat('dd').format(date),
                      style: const TextStyle(
                          color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                  title:
                      Text(DateFormat('EEEE, dd MMMM yyyy', 'ar').format(date)),
                  trailing: Text(
                    NumberFormat.currency(
                      locale: 'ar_SA',
                      symbol: 'ر.س',
                      decimalDigits: 2,
                    ).format(entry.value),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvanceStatusList() {
    final statusCounts =
        _advanceStatistics['statusCounts'] as Map<String, int>? ?? {};

    if (statusCounts.isEmpty) {
      return _buildEmptyChart('لا توجد سلف لعرضها');
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالات السلف',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: statusCounts.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final entry = statusCounts.entries.elementAt(index);
                final status = AdvanceStatus.values.firstWhere(
                  (s) => s.name == entry.key,
                  orElse: () => AdvanceStatus.pending,
                );
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getStatusColor(status),
                    child: Text(
                      entry.value.toString(),
                      style: const TextStyle(
                          color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                  title: Text(status.arabicName),
                  trailing: Text(
                    '${entry.value} سلفة',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyChart(String message) {
    return Card(
      child: SizedBox(
        height: 200,
        width: double.infinity,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.bar_chart,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(int index) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.red,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];
    return colors[index % colors.length];
  }

  Color _getStatusColor(AdvanceStatus status) {
    switch (status) {
      case AdvanceStatus.pending:
        return Colors.orange;
      case AdvanceStatus.partiallyPaid:
        return Colors.blue;
      case AdvanceStatus.fullyPaid:
        return Colors.green;
    }
  }

  void _exportReports() {
    // سيتم تطوير وظيفة التصدير لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('وظيفة التصدير قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
