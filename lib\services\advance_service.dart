import '../database/database_helper.dart';
import '../models/advance.dart';
import '../models/advance_payment.dart';
import '../models/employee.dart';

class AdvanceService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة سلفة جديدة
  Future<int> insertAdvance(Advance advance) async {
    final db = await _databaseHelper.database;
    return await db.insert('advances', advance.toMap());
  }

  // الحصول على جميع السلف
  Future<List<Advance>> getAllAdvances({
    int? limit,
    int? offset,
    String? orderBy,
  }) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        a.*,
        e.name as employee_name,
        e.position as employee_position,
        e.department as employee_department
      FROM advances a
      LEFT JOIN employees e ON a.employee_id = e.id
      ORDER BY ${orderBy ?? 'a.received_date DESC, a.created_at DESC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''');

    return List.generate(maps.length, (i) {
      final advance = Advance.fromMap(maps[i]);
      if (maps[i]['employee_name'] != null) {
        advance.employee = Employee(
          id: advance.employeeId,
          name: maps[i]['employee_name'],
          position: maps[i]['employee_position'],
          department: maps[i]['employee_department'],
        );
      }
      return advance;
    });
  }

  // الحصول على سلفة بواسطة ID
  Future<Advance?> getAdvanceById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        a.*,
        e.name as employee_name,
        e.position as employee_position,
        e.department as employee_department
      FROM advances a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE a.id = ?
    ''', [id]);

    if (maps.isNotEmpty) {
      final advance = Advance.fromMap(maps.first);
      if (maps.first['employee_name'] != null) {
        advance.employee = Employee(
          id: advance.employeeId,
          name: maps.first['employee_name'],
          position: maps.first['employee_position'],
          department: maps.first['employee_department'],
        );
      }
      return advance;
    }
    return null;
  }

  // الحصول على سلف موظف معين
  Future<List<Advance>> getAdvancesByEmployee(int employeeId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        a.*,
        e.name as employee_name,
        e.position as employee_position,
        e.department as employee_department
      FROM advances a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE a.employee_id = ?
      ORDER BY a.received_date DESC
    ''', [employeeId]);

    return List.generate(maps.length, (i) {
      final advance = Advance.fromMap(maps[i]);
      if (maps[i]['employee_name'] != null) {
        advance.employee = Employee(
          id: advance.employeeId,
          name: maps[i]['employee_name'],
          position: maps[i]['employee_position'],
          department: maps[i]['employee_department'],
        );
      }
      return advance;
    });
  }

  // الحصول على السلف المستحقة
  Future<List<Advance>> getOutstandingAdvances() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        a.*,
        e.name as employee_name,
        e.position as employee_position,
        e.department as employee_department
      FROM advances a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE a.status != 'fullyPaid'
      ORDER BY a.received_date ASC
    ''');

    return List.generate(maps.length, (i) {
      final advance = Advance.fromMap(maps[i]);
      if (maps[i]['employee_name'] != null) {
        advance.employee = Employee(
          id: advance.employeeId,
          name: maps[i]['employee_name'],
          position: maps[i]['employee_position'],
          department: maps[i]['employee_department'],
        );
      }
      return advance;
    });
  }

  // تحديث سلفة
  Future<int> updateAdvance(Advance advance) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'advances',
      advance
          .copyWith(
            status: advance.calculatedStatus,
            updatedAt: DateTime.now(),
          )
          .toMap(),
      where: 'id = ?',
      whereArgs: [advance.id],
    );
  }

  // حذف سلفة
  Future<int> deleteAdvance(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'advances',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // إضافة دفعة سداد
  Future<int> addPayment(AdvancePayment payment) async {
    final db = await _databaseHelper.database;

    await db.transaction((txn) async {
      // إضافة دفعة السداد
      await txn.insert('advance_payments', payment.toMap());

      // تحديث المبلغ المدفوع في السلفة
      final result = await txn.rawQuery(
        'SELECT SUM(amount) as total_paid FROM advance_payments WHERE advance_id = ?',
        [payment.advanceId],
      );

      final totalPaid = (result.first['total_paid'] as num?)?.toDouble() ?? 0.0;

      // الحصول على معلومات السلفة
      final advanceResult = await txn.query(
        'advances',
        where: 'id = ?',
        whereArgs: [payment.advanceId],
      );

      if (advanceResult.isNotEmpty) {
        final advance = Advance.fromMap(advanceResult.first);
        final updatedAdvance = advance.copyWith(
          paidAmount: totalPaid,
          status: totalPaid >= advance.amount
              ? AdvanceStatus.fullyPaid
              : totalPaid > 0
                  ? AdvanceStatus.partiallyPaid
                  : AdvanceStatus.pending,
          updatedAt: DateTime.now(),
        );

        await txn.update(
          'advances',
          updatedAdvance.toMap(),
          where: 'id = ?',
          whereArgs: [payment.advanceId],
        );
      }
    });

    return 1;
  }

  // الحصول على دفعات السداد لسلفة معينة
  Future<List<AdvancePayment>> getPaymentsByAdvance(int advanceId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'advance_payments',
      where: 'advance_id = ?',
      whereArgs: [advanceId],
      orderBy: 'payment_date DESC',
    );

    return List.generate(maps.length, (i) {
      return AdvancePayment.fromMap(maps[i]);
    });
  }

  // حذف دفعة سداد
  Future<int> deletePayment(int paymentId) async {
    final db = await _databaseHelper.database;

    // الحصول على معلومات الدفعة قبل حذفها
    final paymentResult = await db.query(
      'advance_payments',
      where: 'id = ?',
      whereArgs: [paymentId],
    );

    if (paymentResult.isEmpty) return 0;

    final payment = AdvancePayment.fromMap(paymentResult.first);

    await db.transaction((txn) async {
      // حذف دفعة السداد
      await txn.delete(
        'advance_payments',
        where: 'id = ?',
        whereArgs: [paymentId],
      );

      // إعادة حساب المبلغ المدفوع
      final result = await txn.rawQuery(
        'SELECT SUM(amount) as total_paid FROM advance_payments WHERE advance_id = ?',
        [payment.advanceId],
      );

      final totalPaid = (result.first['total_paid'] as num?)?.toDouble() ?? 0.0;

      // تحديث السلفة
      final advanceResult = await txn.query(
        'advances',
        where: 'id = ?',
        whereArgs: [payment.advanceId],
      );

      if (advanceResult.isNotEmpty) {
        final advance = Advance.fromMap(advanceResult.first);
        final updatedAdvance = advance.copyWith(
          paidAmount: totalPaid,
          status: totalPaid >= advance.amount
              ? AdvanceStatus.fullyPaid
              : totalPaid > 0
                  ? AdvanceStatus.partiallyPaid
                  : AdvanceStatus.pending,
          updatedAt: DateTime.now(),
        );

        await txn.update(
          'advances',
          updatedAdvance.toMap(),
          where: 'id = ?',
          whereArgs: [payment.advanceId],
        );
      }
    });

    return 1;
  }

  // الحصول على إحصائيات السلف
  Future<Map<String, dynamic>> getAdvanceStatistics() async {
    final db = await _databaseHelper.database;

    // إجمالي السلف
    final totalResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM advances',
    );
    final totalAdvances =
        (totalResult.first['total'] as num?)?.toDouble() ?? 0.0;

    // إجمالي المدفوع
    final paidResult = await db.rawQuery(
      'SELECT SUM(paid_amount) as total FROM advances',
    );
    final totalPaid = (paidResult.first['total'] as num?)?.toDouble() ?? 0.0;

    // السلف المستحقة
    final outstandingResult = await db.rawQuery(
      'SELECT SUM(amount - paid_amount) as outstanding FROM advances WHERE status != ?',
      ['fullyPaid'],
    );
    final outstanding =
        (outstandingResult.first['outstanding'] as num?)?.toDouble() ?? 0.0;

    // عدد السلف حسب الحالة
    final statusResult = await db.rawQuery(
      'SELECT status, COUNT(*) as count FROM advances GROUP BY status',
    );

    Map<String, int> statusCounts = {};
    for (var row in statusResult) {
      statusCounts[row['status'] as String] = row['count'] as int;
    }

    return {
      'totalAdvances': totalAdvances,
      'totalPaid': totalPaid,
      'outstanding': outstanding,
      'statusCounts': statusCounts,
    };
  }
}
