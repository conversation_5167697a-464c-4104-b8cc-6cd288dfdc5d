import '../database/database_helper.dart';
import '../models/employee.dart';

class EmployeeService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة موظف جديد
  Future<int> insertEmployee(Employee employee) async {
    final db = await _databaseHelper.database;
    return await db.insert('employees', employee.toMap());
  }

  // الحصول على جميع الموظفين
  Future<List<Employee>> getAllEmployees() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'employees',
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  // الحصول على موظف بواسطة ID
  Future<Employee?> getEmployeeById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'employees',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Employee.fromMap(maps.first);
    }
    return null;
  }

  // البحث عن الموظفين
  Future<List<Employee>> searchEmployees(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'employees',
      where: 'name LIKE ? OR position LIKE ? OR department LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'name ASC',
    );

    return List.generate(maps.length, (i) {
      return Employee.fromMap(maps[i]);
    });
  }

  // تحديث موظف
  Future<int> updateEmployee(Employee employee) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'employees',
      employee.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [employee.id],
    );
  }

  // حذف موظف
  Future<int> deleteEmployee(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'employees',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // التحقق من وجود سلف مستحقة للموظف
  Future<bool> hasOutstandingAdvances(int employeeId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> result = await db.query(
      'advances',
      where: 'employee_id = ? AND status != ?',
      whereArgs: [employeeId, 'fullyPaid'],
    );
    return result.isNotEmpty;
  }

  // الحصول على إحصائيات الموظف
  Future<Map<String, dynamic>> getEmployeeStatistics(int employeeId) async {
    final db = await _databaseHelper.database;

    // إجمالي السلف
    final totalAdvancesResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM advances WHERE employee_id = ?',
      [employeeId],
    );
    final totalAdvances = totalAdvancesResult.first['total'] ?? 0.0;

    // إجمالي المدفوع
    final totalPaidResult = await db.rawQuery(
      'SELECT SUM(paid_amount) as total FROM advances WHERE employee_id = ?',
      [employeeId],
    );
    final totalPaid = totalPaidResult.first['total'] ?? 0.0;

    // عدد السلف
    final countResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM advances WHERE employee_id = ?',
      [employeeId],
    );
    final advanceCount = countResult.first['count'] ?? 0;

    // السلف المستحقة
    final outstandingResult = await db.rawQuery(
      'SELECT SUM(amount - paid_amount) as outstanding FROM advances WHERE employee_id = ? AND status != ?',
      [employeeId, 'fullyPaid'],
    );
    final outstanding = outstandingResult.first['outstanding'] ?? 0.0;

    return {
      'totalAdvances': totalAdvances,
      'totalPaid': totalPaid,
      'advanceCount': advanceCount,
      'outstanding': outstanding,
    };
  }

  // الحصول على الموظفين مع السلف المستحقة
  Future<List<Map<String, dynamic>>>
      getEmployeesWithOutstandingAdvances() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        e.id,
        e.name,
        e.position,
        e.department,
        SUM(a.amount - a.paid_amount) as outstanding_amount,
        COUNT(a.id) as advance_count
      FROM employees e
      INNER JOIN advances a ON e.id = a.employee_id
      WHERE a.status != 'fullyPaid'
      GROUP BY e.id, e.name, e.position, e.department
      ORDER BY outstanding_amount DESC
    ''');

    return result;
  }
}
