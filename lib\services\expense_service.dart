import '../database/database_helper.dart';
import '../models/expense.dart';

class ExpenseService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة مصروف جديد
  Future<int> insertExpense(Expense expense) async {
    final db = await _databaseHelper.database;
    return await db.insert('expenses', expense.toMap());
  }

  // الحصول على جميع المصروفات
  Future<List<Expense>> getAllExpenses({
    int? limit,
    int? offset,
    String? orderBy,
  }) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      orderBy: orderBy ?? 'date DESC, created_at DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) {
      return Expense.fromMap(maps[i]);
    });
  }

  // الحصول على مصروف بواسطة ID
  Future<Expense?> getExpenseById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Expense.fromMap(maps.first);
    }
    return null;
  }

  // البحث في المصروفات
  Future<List<Expense>> searchExpenses(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'description LIKE ? OR category LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return Expense.fromMap(maps[i]);
    });
  }

  // تصفية المصروفات حسب التاريخ
  Future<List<Expense>> getExpensesByDateRange(
    DateTime startDate,
    DateTime endDate, {
    String? category,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = 'date >= ? AND date <= ?';
    List<dynamic> whereArgs = [
      startDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
    ];

    if (category != null && category.isNotEmpty) {
      whereClause += ' AND category = ?';
      whereArgs.add(category);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return Expense.fromMap(maps[i]);
    });
  }

  // الحصول على المصروفات حسب الفئة
  Future<List<Expense>> getExpensesByCategory(String category) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) {
      return Expense.fromMap(maps[i]);
    });
  }

  // تحديث مصروف
  Future<int> updateExpense(Expense expense) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'expenses',
      expense.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [expense.id],
    );
  }

  // حذف مصروف
  Future<int> deleteExpense(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'expenses',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على إجمالي المصروفات
  Future<double> getTotalExpenses({
    DateTime? startDate,
    DateTime? endDate,
    String? category,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = '1=1';
    List<dynamic> whereArgs = [];

    if (startDate != null && endDate != null) {
      whereClause += ' AND date >= ? AND date <= ?';
      whereArgs.addAll([
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ]);
    }

    if (category != null && category.isNotEmpty) {
      whereClause += ' AND category = ?';
      whereArgs.add(category);
    }

    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM expenses WHERE $whereClause',
      whereArgs,
    );

    return result.first['total']?.toDouble() ?? 0.0;
  }

  // الحصول على إحصائيات المصروفات حسب الفئة
  Future<Map<String, double>> getExpensesByCategories({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await _databaseHelper.database;

    String whereClause = '1=1';
    List<dynamic> whereArgs = [];

    if (startDate != null && endDate != null) {
      whereClause += ' AND date >= ? AND date <= ?';
      whereArgs.addAll([
        startDate.toIso8601String().split('T')[0],
        endDate.toIso8601String().split('T')[0],
      ]);
    }

    final List<Map<String, dynamic>> result = await db.rawQuery(
      'SELECT category, SUM(amount) as total FROM expenses WHERE $whereClause GROUP BY category ORDER BY total DESC',
      whereArgs,
    );

    Map<String, double> categoryTotals = {};
    for (var row in result) {
      categoryTotals[row['category']] = row['total']?.toDouble() ?? 0.0;
    }

    return categoryTotals;
  }

  // الحصول على المصروفات اليومية للأسبوع الماضي
  Future<Map<String, double>> getDailyExpensesForWeek() async {
    final db = await _databaseHelper.database;
    final DateTime endDate = DateTime.now();
    final DateTime startDate = endDate.subtract(const Duration(days: 7));

    final List<Map<String, dynamic>> result = await db.rawQuery('''
      SELECT 
        date(date) as expense_date,
        SUM(amount) as total
      FROM expenses 
      WHERE date >= ? AND date <= ?
      GROUP BY date(date)
      ORDER BY expense_date
    ''', [
      startDate.toIso8601String().split('T')[0],
      endDate.toIso8601String().split('T')[0],
    ]);

    Map<String, double> dailyTotals = {};
    for (var row in result) {
      dailyTotals[row['expense_date']] = row['total']?.toDouble() ?? 0.0;
    }

    return dailyTotals;
  }

  // الحصول على أعلى المصروفات
  Future<List<Expense>> getTopExpenses({int limit = 10}) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'expenses',
      orderBy: 'amount DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return Expense.fromMap(maps[i]);
    });
  }
}
