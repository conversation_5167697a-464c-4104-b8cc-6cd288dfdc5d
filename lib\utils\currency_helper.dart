import 'package:intl/intl.dart';

class CurrencyHelper {
  // رمز العملة اليمنية
  static const String currencySymbol = '﷼';
  static const String currencyCode = 'YER';
  static const String currencyName = 'ريال يمني';
  
  // تنسيق العملة اليمنية
  static final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'ar_YE',
    symbol: currencySymbol,
    decimalDigits: 0, // الريال اليمني عادة لا يستخدم الكسور العشرية
  );
  
  // تنسيق العملة مع الكسور العشرية (للحسابات الدقيقة)
  static final NumberFormat _currencyFormatterWithDecimals = NumberFormat.currency(
    locale: 'ar_YE',
    symbol: currencySymbol,
    decimalDigits: 2,
  );
  
  // تنسيق الأرقام بدون رمز العملة
  static final NumberFormat _numberFormatter = NumberFormat(
    '#,##0',
    'ar_YE',
  );
  
  // تنسيق الأرقام مع الكسور العشرية
  static final NumberFormat _numberFormatterWithDecimals = NumberFormat(
    '#,##0.00',
    'ar_YE',
  );

  /// تنسيق المبلغ كعملة يمنية
  static String formatCurrency(double amount, {bool showDecimals = false}) {
    if (showDecimals) {
      return _currencyFormatterWithDecimals.format(amount);
    } else {
      return _currencyFormatter.format(amount);
    }
  }

  /// تنسيق المبلغ كرقم بدون رمز العملة
  static String formatNumber(double amount, {bool showDecimals = false}) {
    if (showDecimals) {
      return _numberFormatterWithDecimals.format(amount);
    } else {
      return _numberFormatter.format(amount);
    }
  }

  /// تحويل النص إلى رقم
  static double? parseAmount(String text) {
    if (text.isEmpty) return null;
    
    // إزالة رمز العملة والمسافات والفواصل
    String cleanText = text
        .replaceAll(currencySymbol, '')
        .replaceAll(',', '')
        .replaceAll(' ', '')
        .trim();
    
    return double.tryParse(cleanText);
  }

  /// التحقق من صحة المبلغ
  static bool isValidAmount(String text) {
    final amount = parseAmount(text);
    return amount != null && amount >= 0;
  }

  /// تنسيق المبلغ للعرض في البطاقات
  static String formatForCard(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)} مليون $currencySymbol';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} ألف $currencySymbol';
    } else {
      return formatCurrency(amount);
    }
  }

  /// تنسيق المبلغ مع النص الوصفي
  static String formatWithDescription(double amount, String description) {
    return '${formatCurrency(amount)} - $description';
  }

  /// الحصول على لون المبلغ حسب القيمة
  static String getAmountColor(double amount) {
    if (amount > 0) {
      return 'success'; // أخضر للمبالغ الموجبة
    } else if (amount < 0) {
      return 'error'; // أحمر للمبالغ السالبة
    } else {
      return 'neutral'; // رمادي للصفر
    }
  }

  /// تحويل المبلغ إلى كلمات (باللغة العربية)
  static String amountToWords(double amount) {
    if (amount == 0) return 'صفر ريال يمني';
    
    int intAmount = amount.toInt();
    String result = _convertNumberToWords(intAmount);
    
    if (intAmount == 1) {
      result += ' ريال يمني واحد';
    } else if (intAmount == 2) {
      result += ' ريالان يمنيان';
    } else if (intAmount <= 10) {
      result += ' ريالات يمنية';
    } else {
      result += ' ريال يمني';
    }
    
    return result;
  }

  // مساعد لتحويل الأرقام إلى كلمات
  static String _convertNumberToWords(int number) {
    if (number == 0) return 'صفر';
    
    List<String> ones = [
      '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة',
      'ستة', 'سبعة', 'ثمانية', 'تسعة'
    ];
    
    List<String> tens = [
      '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون',
      'ستون', 'سبعون', 'ثمانون', 'تسعون'
    ];
    
    List<String> teens = [
      'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر',
      'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    ];
    
    if (number < 10) {
      return ones[number];
    } else if (number < 20) {
      return teens[number - 10];
    } else if (number < 100) {
      int tensDigit = number ~/ 10;
      int onesDigit = number % 10;
      return '${tens[tensDigit]}${onesDigit > 0 ? ' و${ones[onesDigit]}' : ''}';
    } else if (number < 1000) {
      int hundreds = number ~/ 100;
      int remainder = number % 100;
      String result = '${ones[hundreds]} مائة';
      if (remainder > 0) {
        result += ' و${_convertNumberToWords(remainder)}';
      }
      return result;
    } else if (number < 1000000) {
      int thousands = number ~/ 1000;
      int remainder = number % 1000;
      String result = '${_convertNumberToWords(thousands)} ألف';
      if (remainder > 0) {
        result += ' و${_convertNumberToWords(remainder)}';
      }
      return result;
    } else {
      int millions = number ~/ 1000000;
      int remainder = number % 1000000;
      String result = '${_convertNumberToWords(millions)} مليون';
      if (remainder > 0) {
        result += ' و${_convertNumberToWords(remainder)}';
      }
      return result;
    }
  }

  /// الحصول على معلومات العملة
  static Map<String, String> getCurrencyInfo() {
    return {
      'symbol': currencySymbol,
      'code': currencyCode,
      'name': currencyName,
      'locale': 'ar_YE',
    };
  }

  /// تنسيق التاريخ والمبلغ معاً
  static String formatDateAndAmount(DateTime date, double amount) {
    final dateFormat = DateFormat('dd/MM/yyyy', 'ar');
    return '${formatCurrency(amount)} - ${dateFormat.format(date)}';
  }
}
